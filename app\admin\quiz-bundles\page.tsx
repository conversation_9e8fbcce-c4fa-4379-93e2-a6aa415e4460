'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  Users, 
  BookOpen,
  DollarSign,
  TrendingUp,
  Package
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { BundleForm } from '@/components/admin/quiz-bundles/bundle-form'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  tags: string[]
  features: string[]
  isActive: boolean
  isPublished: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  quizCount: number
  purchaseCount: number
  quizzes: Array<{
    id: string
    title: string
    type: string
    difficulty: string
    order: number
  }>
}

export default function AdminQuizBundlesPage() {
  const router = useRouter()
  const [bundles, setBundles] = useState<Bundle[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingBundle, setEditingBundle] = useState<Bundle | null>(null)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    fetchBundles()
  }, [])

  const fetchBundles = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        published: 'false', // Get all bundles for admin
        limit: '100'
      })

      const response = await fetch(`/api/quiz-bundles?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBundles(data.data.bundles || [])
      } else {
        toast.error('Failed to load quiz bundles')
      }
    } catch (error) {
      console.error('Error fetching bundles:', error)
      toast.error('Failed to load quiz bundles')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBundle = async (bundleData: any) => {
    try {
      setSubmitting(true)
      const response = await fetch('/api/quiz-bundles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bundleData)
      })

      if (response.ok) {
        toast.success('Quiz bundle created successfully!')
        setShowCreateModal(false)
        fetchBundles()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to create bundle')
      }
    } catch (error) {
      console.error('Error creating bundle:', error)
      toast.error('Failed to create bundle')
    } finally {
      setSubmitting(false)
    }
  }

  const handleUpdateBundle = async (bundleData: any) => {
    if (!editingBundle) return

    try {
      setSubmitting(true)
      const response = await fetch(`/api/quiz-bundles/${editingBundle.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bundleData)
      })

      if (response.ok) {
        toast.success('Quiz bundle updated successfully!')
        setEditingBundle(null)
        fetchBundles()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update bundle')
      }
    } catch (error) {
      console.error('Error updating bundle:', error)
      toast.error('Failed to update bundle')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteBundle = async (bundleId: string) => {
    if (!confirm('Are you sure you want to delete this bundle? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/quiz-bundles/${bundleId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Bundle deleted successfully!')
        fetchBundles()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to delete bundle')
      }
    } catch (error) {
      console.error('Error deleting bundle:', error)
      toast.error('Failed to delete bundle')
    }
  }

  const filteredBundles = bundles.filter(bundle => {
    const matchesSearch = bundle.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bundle.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || bundle.category === categoryFilter
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'published' && bundle.isPublished) ||
                         (statusFilter === 'draft' && !bundle.isPublished) ||
                         (statusFilter === 'active' && bundle.isActive) ||
                         (statusFilter === 'inactive' && !bundle.isActive)

    return matchesSearch && matchesCategory && matchesStatus
  })

  const stats = {
    total: bundles.length,
    published: bundles.filter(b => b.isPublished).length,
    draft: bundles.filter(b => !b.isPublished).length,
    totalRevenue: bundles.reduce((sum, b) => sum + (b.price * b.purchaseCount), 0)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
              Quiz Bundles
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Create and manage quiz bundles for students
            </p>
          </div>
          <Button onClick={() => setShowCreateModal(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Bundle
          </Button>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Bundles</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Published</p>
                  <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                </div>
                <Eye className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Draft</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.draft}</p>
                </div>
                <Edit className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Revenue</p>
                  <p className="text-2xl font-bold text-purple-600">₹{stats.totalRevenue.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-col md:flex-row gap-4 mb-6"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search bundles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="Mathematics">Mathematics</SelectItem>
              <SelectItem value="Science">Science</SelectItem>
              <SelectItem value="English">English</SelectItem>
              <SelectItem value="General Knowledge">General Knowledge</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Bundles List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-4"
        >
          {filteredBundles.length === 0 ? (
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                  No bundles found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all'
                    ? "No bundles match your current filters."
                    : "Get started by creating your first quiz bundle."
                  }
                </p>
                {(!searchTerm && categoryFilter === 'all' && statusFilter === 'all') && (
                  <Button onClick={() => setShowCreateModal(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Bundle
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            filteredBundles.map((bundle, index) => (
              <motion.div
                key={bundle.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        {/* Bundle Image */}
                        <div className="w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          {bundle.thumbnailImage ? (
                            <img
                              src={bundle.thumbnailImage}
                              alt={bundle.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <BookOpen className="h-8 w-8 text-white" />
                          )}
                        </div>

                        {/* Bundle Info */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                              {bundle.title}
                            </h3>
                            <div className="flex items-center gap-2">
                              {bundle.isPublished ? (
                                <Badge className="bg-green-100 text-green-800">Published</Badge>
                              ) : (
                                <Badge variant="secondary">Draft</Badge>
                              )}
                              {!bundle.isActive && (
                                <Badge variant="destructive">Inactive</Badge>
                              )}
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-1">
                            {bundle.shortDescription || bundle.description || 'No description'}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <BookOpen className="h-4 w-4" />
                              {bundle.quizCount} quizzes
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {bundle.purchaseCount} purchases
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              ₹{bundle.price}
                            </div>
                            {bundle.category && (
                              <Badge variant="outline" className="text-xs">
                                {bundle.category}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/quiz-bundles/${bundle.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingBundle(bundle)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteBundle(bundle.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </motion.div>

        {/* Create Bundle Modal */}
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Quiz Bundle</DialogTitle>
            </DialogHeader>
            <BundleForm
              onSubmit={handleCreateBundle}
              onCancel={() => setShowCreateModal(false)}
              isLoading={submitting}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Bundle Modal */}
        <Dialog open={!!editingBundle} onOpenChange={() => setEditingBundle(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Quiz Bundle</DialogTitle>
            </DialogHeader>
            {editingBundle && (
              <BundleForm
                bundle={editingBundle}
                onSubmit={handleUpdateBundle}
                onCancel={() => setEditingBundle(null)}
                isLoading={submitting}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
