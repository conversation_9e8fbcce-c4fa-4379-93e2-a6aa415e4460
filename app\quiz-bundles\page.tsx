'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Filter, 
  BookOpen, 
  Package,
  Star,
  TrendingUp,
  Users
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BundleCard } from '@/components/student/quiz-bundles/bundle-card'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  quizCount: number
  quizzes: Array<{
    id: string
    title: string
    type: string
    difficulty: string
    timeLimit?: number
  }>
  purchaseCount: number
}

export default function QuizBundlesPage() {
  const router = useRouter()
  const [bundles, setBundles] = useState<Bundle[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [levelFilter, setLevelFilter] = useState('all')
  const [priceFilter, setPriceFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  useEffect(() => {
    fetchBundles()
  }, [])

  const fetchBundles = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/quiz-bundles?published=true&limit=100')
      
      if (response.ok) {
        const data = await response.json()
        setBundles(data.data.bundles || [])
      } else {
        toast.error('Failed to load quiz bundles')
      }
    } catch (error) {
      console.error('Error fetching bundles:', error)
      toast.error('Failed to load quiz bundles')
    } finally {
      setLoading(false)
    }
  }

  const handlePurchaseSuccess = () => {
    // Redirect to student dashboard after purchase
    router.push('/student/quiz-bundles')
  }

  // Filter and sort bundles
  const filteredAndSortedBundles = bundles
    .filter(bundle => {
      const matchesSearch = bundle.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           bundle.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           bundle.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = categoryFilter === 'all' || bundle.category === categoryFilter
      const matchesLevel = levelFilter === 'all' || bundle.level === levelFilter
      
      let matchesPrice = true
      if (priceFilter === 'free') {
        matchesPrice = bundle.price === 0
      } else if (priceFilter === 'paid') {
        matchesPrice = bundle.price > 0
      } else if (priceFilter === 'under-500') {
        matchesPrice = bundle.price < 500
      } else if (priceFilter === '500-1000') {
        matchesPrice = bundle.price >= 500 && bundle.price <= 1000
      } else if (priceFilter === 'over-1000') {
        matchesPrice = bundle.price > 1000
      }

      return matchesSearch && matchesCategory && matchesLevel && matchesPrice
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price
        case 'price-high':
          return b.price - a.price
        case 'popular':
          return b.purchaseCount - a.purchaseCount
        case 'quizzes':
          return b.quizCount - a.quizCount
        case 'newest':
        default:
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
      }
    })

  const stats = {
    totalBundles: bundles.length,
    freeBundles: bundles.filter(b => b.price === 0).length,
    paidBundles: bundles.filter(b => b.price > 0).length,
    totalQuizzes: bundles.reduce((sum, b) => sum + b.quizCount, 0)
  }

  const categories = Array.from(new Set(bundles.map(b => b.category).filter(Boolean)))
  const levels = Array.from(new Set(bundles.map(b => b.level).filter(Boolean)))

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-96 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent mb-4">
            Quiz Bundles
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Discover curated collections of quizzes designed to accelerate your learning journey. 
            From beginner-friendly bundles to advanced challenges, find the perfect fit for your goals.
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <Package className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{stats.totalBundles}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Bundles</div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <Star className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">{stats.freeBundles}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Free Bundles</div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">{stats.paidBundles}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Premium Bundles</div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6 text-center">
              <BookOpen className="h-8 w-8 text-orange-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">{stats.totalQuizzes}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Quizzes</div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-col lg:flex-row gap-4 mb-8"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search quiz bundles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={levelFilter} onValueChange={setLevelFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              {levels.map(level => (
                <SelectItem key={level} value={level}>{level}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={priceFilter} onValueChange={setPriceFilter}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Price" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Prices</SelectItem>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="under-500">Under ₹500</SelectItem>
              <SelectItem value="500-1000">₹500 - ₹1000</SelectItem>
              <SelectItem value="over-1000">Over ₹1000</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full lg:w-48">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="quizzes">Most Quizzes</SelectItem>
            </SelectContent>
          </Select>
        </motion.div>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-6"
        >
          <p className="text-gray-600 dark:text-gray-300">
            Showing {filteredAndSortedBundles.length} of {bundles.length} quiz bundles
          </p>
        </motion.div>

        {/* Bundles Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {filteredAndSortedBundles.length === 0 ? (
            <div className="col-span-full">
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                <CardContent className="p-12 text-center">
                  <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-800 dark:text-white mb-2">
                    No bundles found
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    {searchTerm || categoryFilter !== 'all' || levelFilter !== 'all' || priceFilter !== 'all'
                      ? "No bundles match your current filters. Try adjusting your search criteria."
                      : "No quiz bundles are available at the moment. Check back soon!"
                    }
                  </p>
                  {(searchTerm || categoryFilter !== 'all' || levelFilter !== 'all' || priceFilter !== 'all') && (
                    <div className="flex flex-wrap gap-2 justify-center">
                      {searchTerm && (
                        <button
                          onClick={() => setSearchTerm('')}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          Clear search
                        </button>
                      )}
                      {categoryFilter !== 'all' && (
                        <button
                          onClick={() => setCategoryFilter('all')}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          Clear category
                        </button>
                      )}
                      {levelFilter !== 'all' && (
                        <button
                          onClick={() => setLevelFilter('all')}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          Clear level
                        </button>
                      )}
                      {priceFilter !== 'all' && (
                        <button
                          onClick={() => setPriceFilter('all')}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          Clear price
                        </button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            filteredAndSortedBundles.map((bundle, index) => (
              <motion.div
                key={bundle.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <BundleCard
                  bundle={bundle}
                  onPurchaseSuccess={handlePurchaseSuccess}
                />
              </motion.div>
            ))
          )}
        </motion.div>

        {/* Call to Action */}
        {filteredAndSortedBundles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center mt-12"
          >
            <Card className="bg-gradient-to-r from-blue-500 to-purple-600 border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-white mb-4">
                  Ready to Start Learning?
                </h3>
                <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                  Join thousands of students who are already improving their skills with our curated quiz bundles. 
                  Start your learning journey today!
                </p>
                <div className="flex items-center justify-center gap-4 text-sm text-blue-100">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>10,000+ Students</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    <span>4.8/5 Rating</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span>500+ Quizzes</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )
}
