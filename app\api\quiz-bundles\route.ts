import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { quizBundleService } from '@/lib/quiz-bundle-service'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createBundleSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be non-negative'),
  originalPrice: z.number().optional(),
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  thumbnailImage: z.string().url().optional(),
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  duration: z.string().optional(),
  tags: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  quizIds: z.array(z.string()).min(1, 'At least one quiz is required')
})

const querySchema = commonSchemas.pagination.extend({
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  search: z.string().optional(),
  minPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  maxPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  published: z.string().optional().transform(val => {
    if (val === undefined) return undefined
    return val === 'true' || val === '1'
  }),
  slug: z.string().optional()
})

// GET /api/quiz-bundles - Get quiz bundles (public for students, all for admins)
export const GET = createAPIHandler(
  {
    requireAuth: false,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const {
        page = 1,
        limit = 20,
        category,
        level,
        search,
        minPrice,
        maxPrice,
        published
      } = validatedQuery

      // For non-admin users, only show published bundles
      const isAdmin = user?.role === 'ADMIN'
      const shouldShowPublished = published !== undefined ? published : !isAdmin

      console.log('Quiz bundles API debug:', {
        user: user ? { id: user.id, role: user.role } : null,
        isAdmin,
        published,
        shouldShowPublished,
        queryParams: { page, limit, category, level, search, minPrice, maxPrice }
      })

      // If requesting unpublished bundles but not admin, return error
      if (published === false && !isAdmin) {
        console.log('Non-admin user requesting unpublished bundles - access denied')
        return APIResponse.error('Access denied. Admin privileges required to view unpublished bundles.', 403)
      }

      let bundles
      
      if (shouldShowPublished) {
        // Get published bundles for students
        const filters: any = {}
        
        if (category) filters.category = category
        if (level) filters.level = level
        if (search) filters.search = search
        if (minPrice !== undefined || maxPrice !== undefined) {
          filters.priceRange = {
            min: minPrice || 0,
            max: maxPrice || Number.MAX_SAFE_INTEGER
          }
        }

        bundles = await quizBundleService.getPublishedBundles(filters)
      } else {
        // Get all bundles for admin
        const where: any = {}
        
        if (category) where.category = category
        if (level) where.level = level
        if (search) {
          where.OR = [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }
        if (minPrice !== undefined || maxPrice !== undefined) {
          where.price = {}
          if (minPrice !== undefined) where.price.gte = minPrice
          if (maxPrice !== undefined) where.price.lte = maxPrice
        }

        const allBundles = await prisma.quizBundle.findMany({
          where,
          include: {
            items: {
              include: {
                quiz: {
                  select: {
                    id: true,
                    title: true,
                    type: true,
                    difficulty: true,
                    timeLimit: true
                  }
                }
              },
              orderBy: { order: 'asc' }
            },
            _count: {
              select: {
                purchases: {
                  where: { status: 'active' }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        })

        bundles = allBundles
      }

      // Paginate results
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedBundles = bundles.slice(startIndex, endIndex)

      // Format response
      const formattedBundles = paginatedBundles.map(bundle => ({
        id: bundle.id,
        title: bundle.title,
        description: bundle.description,
        shortDescription: bundle.shortDescription,
        price: bundle.price,
        originalPrice: bundle.originalPrice,
        slug: bundle.slug,
        thumbnailImage: bundle.thumbnailImage,
        category: bundle.category,
        level: bundle.level,
        duration: bundle.duration,
        tags: bundle.tags,
        features: bundle.features,
        isActive: bundle.isActive,
        isPublished: bundle.isPublished,
        publishedAt: bundle.publishedAt,
        createdAt: bundle.createdAt,
        updatedAt: bundle.updatedAt,
        quizCount: bundle.items?.length || 0,
        quizzes: bundle.items?.map(item => ({
          id: item.quiz.id,
          title: item.quiz.title,
          type: item.quiz.type,
          difficulty: item.quiz.difficulty,
          timeLimit: item.quiz.timeLimit,
          order: item.order,
          isRequired: item.isRequired
        })) || [],
        purchaseCount: bundle._count?.purchases || 0
      }))

      return APIResponse.success({
        bundles: formattedBundles,
        pagination: {
          page,
          limit,
          total: bundles.length,
          totalPages: Math.ceil(bundles.length / limit),
          hasNext: endIndex < bundles.length,
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching quiz bundles:', error)
      return APIResponse.error('Failed to fetch quiz bundles', 500)
    }
  }
)

// POST /api/quiz-bundles - Create new quiz bundle (admin only)
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createBundleSchema
  },
  async (request: NextRequest, { validatedBody }) => {
    try {
      const bundle = await quizBundleService.createBundle(validatedBody)

      return APIResponse.success({
        message: 'Quiz bundle created successfully',
        bundle: {
          id: bundle.id,
          title: bundle.title,
          slug: bundle.slug,
          price: bundle.price,
          quizCount: bundle.items.length
        }
      }, 201)

    } catch (error: any) {
      console.error('Error creating quiz bundle:', error)
      
      if (error.message.includes('slug already exists')) {
        return APIResponse.error('Bundle slug already exists', 400)
      }
      
      if (error.message.includes('not found or not published')) {
        return APIResponse.error('Some quizzes are not available', 400)
      }

      return APIResponse.error('Failed to create quiz bundle', 500)
    }
  }
)
