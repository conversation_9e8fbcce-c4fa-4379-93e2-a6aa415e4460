"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession, signOut } from "next-auth/react"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { initializeSocket } from "@/lib/socket-client"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  LayoutDashboard,
  BookOpen,
  Trophy,
  User,
  Settings,
  Menu,
  Play,
  Calendar,
  BarChart3,
  Award,
  Clock,
  Target,
  LogOut,
  Moon,
  Sun,
  ChevronDown,
  MessageCircle,
  Heart,
  Star,
  Zap,
  GraduationCap,
  Library
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSep<PERSON><PERSON>,
  Dropdown<PERSON>enu<PERSON>rigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { useSystemSettings } from "@/hooks/use-system-settings"

interface StudentLayoutProps {
  children: React.ReactNode
}

interface UserData {
  name: string
  email: string
  image?: string
  level: number
  totalPoints: number
}

const navigationItems = [
  {
    title: "Dashboard",
    href: "/student",
    icon: LayoutDashboard,
    description: "Overview and progress"
  },
  {
    title: "Browse Quizzes",
    href: "/student/browse",
    icon: BookOpen,
    description: "Find and take quizzes"
  },
  {
    title: "Daily Practice",
    href: "/student/practice",
    icon: Play,
    description: "Quick practice sessions"
  },
  {
    title: "Live Quiz",
    href: "/student/live-quiz",
    icon: Zap,
    description: "Join live quiz sessions"
  },
  {
    title: "Courses",
    href: "/student/courses",
    icon: GraduationCap,
    description: "Browse and enroll in courses"
  },
  {
    title: "My Courses",
    href: "/student/my-courses",
    icon: Library,
    description: "Your enrolled courses"
  },
  {
    title: "My History",
    href: "/student/history",
    icon: Clock,
    description: "Past attempts and scores"
  },
  {
    title: "Favorites",
    href: "/student/favorites",
    icon: Heart,
    description: "Your bookmarked quizzes"
  },
  {
    title: "Analytics",
    href: "/student/analytics",
    icon: BarChart3,
    description: "Performance insights"
  },
  {
    title: "Achievements",
    href: "/student/achievements",
    icon: Award,
    description: "Badges and milestones"
  },
  {
    title: "Leaderboard",
    href: "/student/leaderboard",
    icon: Trophy,
    description: "Rankings and competition"
  },
  {
    title: "Schedule",
    href: "/student/schedule",
    icon: Calendar,
    description: "Upcoming quizzes"
  },
 
  {
    title: "Chat",
    href: "/student/chat",
    icon: MessageCircle,
    description: "Real-time messaging and discussions"
  }
]

export function StudentLayout({ children }: StudentLayoutProps) {
  const { settings } = useSystemSettings()
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const { data: session } = useSession()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserData = async () => {
      if (!session?.user?.id) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch('/api/student/profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            // Calculate level from total points
            const calculateLevel = (points: number): number => {
              return Math.floor(Math.sqrt(points / 100)) + 1
            }

            setUserData({
              name: data.data.name || session.user.name || 'Student',
              email: data.data.email || session.user.email || '',
              image: data.data.avatar || session.user.image,
              level: calculateLevel(data.data.stats?.totalPoints || 0),
              totalPoints: data.data.stats?.totalPoints || 0
            })

            // Initialize socket connection for real-time notifications
            if (session.user) {
              console.log('🔌 Initializing socket for student:', session.user.name)
              initializeSocket({
                id: session.user.id,
                name: session.user.name || 'Student',
                email: session.user.email || '',
                role: session.user.role || 'STUDENT',
                token: 'session-token'
              })
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserData()
  }, [session])

  const NavItems = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="space-y-2">
      {navigationItems.map((item) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "group flex items-center gap-3 rounded-xl px-4 py-3 text-sm transition-all duration-300 hover:shadow-lg",
              isActive
                ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"
                : "text-muted-foreground hover:bg-white/50 dark:hover:bg-gray-800/50 hover:text-foreground backdrop-blur-sm",
              mobile && "text-base py-4"
            )}
          >
            <div className={cn(
              "p-2 rounded-lg transition-all duration-300",
              isActive
                ? "bg-white/20 text-white"
                : "bg-gray-100 dark:bg-gray-800 text-muted-foreground group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 group-hover:text-blue-600"
            )}>
              <item.icon className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <div className={cn(
                "font-medium transition-colors",
                isActive ? "text-white" : "group-hover:text-foreground"
              )}>
                {item.title}
              </div>
              {mobile && (
                <div className={cn(
                  "text-xs transition-colors",
                  isActive ? "text-white/80" : "text-muted-foreground"
                )}>
                  {item.description}
                </div>
              )}
            </div>
            {isActive && (
              <div className="w-2 h-2 bg-white rounded-full shadow-lg" />
            )}
          </Link>
        )
      })}
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-72 md:flex-col md:fixed md:inset-y-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-r border-white/20 dark:border-gray-800/20 shadow-xl">
        <div className="flex flex-col flex-1 min-h-0">
          {/* Logo */}
          <div className="flex items-center h-20 px-6 border-b border-white/20 dark:border-gray-800/20">
            <Link href="/student" className="flex items-center gap-3 group">
              {settings.logoUrl ? (
                <img src={settings.logoUrl} alt={settings.companyName} className="w-12 h-12 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300" />
              ) : (
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Target className="h-7 w-7 text-white" />
                </div>
              )}
              <div>
                <h1 className="font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{settings.companyName}</h1>
                <p className="text-sm text-muted-foreground">Student Portal</p>
              </div>
            </Link>
          </div>



          {/* Navigation */}
          <ScrollArea className="flex-1 px-4 py-4">
            <NavItems />
          </ScrollArea>

          {/* User Profile */}
          <div className="p-4 border-t border-white/20 dark:border-gray-800/20">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full justify-start p-3 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all duration-300 shadow-lg">
                  <Avatar className="h-10 w-10 mr-3 ring-2 ring-blue-500/20 dark:ring-gray-800/20 shadow-lg">
                    <AvatarImage src={userData?.image || ""} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-bold">
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0 text-left">
                    <div className="text-sm font-bold truncate" title={userData?.name || "Student"}>
                      {loading ? "Loading..." : (userData?.name || "Student")}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {loading ? "..." : (
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                          <span className="truncate">Level {userData?.level || 1} • {userData?.totalPoints?.toLocaleString() || 0} pts</span>
                        </span>
                      )}
                    </div>
                  </div>
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/student/profile">
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/student/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                  {theme === "dark" ? (
                    <Sun className="h-4 w-4 mr-2" />
                  ) : (
                    <Moon className="h-4 w-4 mr-2" />
                  )}
                  Toggle Theme
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600 cursor-pointer"
                  onClick={() => signOut({ callbackUrl: '/' })}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="md:pl-72">
        {/* Desktop Header */}
        <div className="hidden md:flex h-20 items-center justify-between gap-4 px-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-white/20 dark:border-gray-800/20 shadow-lg">
          <div className="flex-1">
            <h2 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Student Dashboard</h2>
          </div>

          <div className="flex items-center gap-3">
            <NotificationCenter />
          </div>
        </div>

        {/* Mobile Header */}
        <div className="flex h-16 items-center gap-4 px-4 md:hidden border-b bg-background">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-4 w-4" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <div className="flex items-center gap-2 px-2 py-4">
                {settings.logoUrl ? (
                  <img src={settings.logoUrl} alt={settings.companyName} className="w-8 h-8 rounded-lg" />
                ) : (
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                )}
                <div>
                  <h1 className="font-bold text-lg">{settings.companyName}</h1>
                  <p className="text-xs text-muted-foreground">Student Portal</p>
                </div>
              </div>
              


              <ScrollArea className="flex-1 px-2 py-4">
                <NavItems mobile />
              </ScrollArea>

              <div className="px-2 py-4 border-t">
                <div className="flex items-center gap-3 p-2">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={userData?.image || " "} />
                    <AvatarFallback>
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="text-sm font-medium">
                      {loading ? "Loading..." : (userData?.name || "Student")}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {loading ? "..." : `Level ${userData?.level || 1} • ${userData?.totalPoints?.toLocaleString() || 0} pts`}
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <div className="flex-1">
            <Link href="/student" className="flex items-center gap-2">
              {settings.logoUrl ? (
                <img src={settings.logoUrl} alt={settings.companyName} className="w-8 h-8 rounded-lg" />
              ) : (
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Target className="h-5 w-5 text-white" />
                </div>
              )}
              <h1 className="font-bold text-lg">{settings.companyName}</h1>
            </Link>
          </div>

          <div className="flex items-center gap-2">
            <NotificationCenter />
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={userData?.image || " "} />
                    <AvatarFallback>
                      {loading ? "..." : (userData?.name?.split(' ').map(n => n[0]).join('') || "ST")}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/student/profile">
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/student/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                  {theme === "dark" ? (
                    <Sun className="h-4 w-4 mr-2" />
                  ) : (
                    <Moon className="h-4 w-4 mr-2" />
                  )}
                  Toggle Theme
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600 cursor-pointer"
                  onClick={() => signOut({ callbackUrl: '/' })}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 relative">
          {children}
        </main>
      </div>
    </div>
  )
}
