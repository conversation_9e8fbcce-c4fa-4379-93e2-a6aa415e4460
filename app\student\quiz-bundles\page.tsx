'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Filter, 
  BookOpen, 
  TrendingUp,
  Package,
  ShoppingCart,
  CheckCircle
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BundleCard } from '@/components/student/quiz-bundles/bundle-card'
import { toast } from 'sonner'

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  quizCount: number
  quizzes: Array<{
    id: string
    title: string
    type: string
    difficulty: string
    timeLimit?: number
  }>
  purchaseCount: number
}

interface PurchasedBundle {
  id: string
  bundleId: string
  status: string
  progress: number
  purchasedAt: string
  bundle: Bundle
  calculatedProgress: {
    totalQuizzes: number
    completedQuizzes: number
    progressPercentage: number
  }
}

export default function StudentQuizBundlesPage() {
  const [availableBundles, setAvailableBundles] = useState<Bundle[]>([])
  const [purchasedBundles, setPurchasedBundles] = useState<PurchasedBundle[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [levelFilter, setLevelFilter] = useState('all')
  const [priceFilter, setPriceFilter] = useState('all')
  const [activeTab, setActiveTab] = useState<'available' | 'purchased'>('available')

  useEffect(() => {
    fetchBundles()
  }, [])

  const fetchBundles = async () => {
    try {
      setLoading(true)
      
      // Fetch available bundles
      const availableResponse = await fetch('/api/quiz-bundles?published=true&limit=100')
      if (availableResponse.ok) {
        const availableData = await availableResponse.json()
        setAvailableBundles(availableData.data.bundles || [])
      }

      // Fetch purchased bundles
      const purchasedResponse = await fetch('/api/student/quiz-bundles')
      if (purchasedResponse.ok) {
        const purchasedData = await purchasedResponse.json()
        setPurchasedBundles(purchasedData.data.bundles || [])
      }
    } catch (error) {
      console.error('Error fetching bundles:', error)
      toast.error('Failed to load quiz bundles')
    } finally {
      setLoading(false)
    }
  }

  const handlePurchaseSuccess = () => {
    fetchBundles() // Refresh both lists
  }

  // Filter available bundles
  const filteredAvailableBundles = availableBundles.filter(bundle => {
    const matchesSearch = bundle.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bundle.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bundle.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = categoryFilter === 'all' || bundle.category === categoryFilter
    const matchesLevel = levelFilter === 'all' || bundle.level === levelFilter
    
    let matchesPrice = true
    if (priceFilter === 'free') {
      matchesPrice = bundle.price === 0
    } else if (priceFilter === 'paid') {
      matchesPrice = bundle.price > 0
    } else if (priceFilter === 'under-500') {
      matchesPrice = bundle.price < 500
    } else if (priceFilter === '500-1000') {
      matchesPrice = bundle.price >= 500 && bundle.price <= 1000
    } else if (priceFilter === 'over-1000') {
      matchesPrice = bundle.price > 1000
    }

    // Check if already purchased
    const isPurchased = purchasedBundles.some(p => p.bundleId === bundle.id && p.status === 'active')

    return matchesSearch && matchesCategory && matchesLevel && matchesPrice && !isPurchased
  })

  // Filter purchased bundles
  const filteredPurchasedBundles = purchasedBundles.filter(purchase => {
    const bundle = purchase.bundle
    const matchesSearch = bundle.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bundle.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = categoryFilter === 'all' || bundle.category === categoryFilter
    const matchesLevel = levelFilter === 'all' || bundle.level === levelFilter

    return matchesSearch && matchesCategory && matchesLevel
  })

  const stats = {
    availableCount: availableBundles.length,
    purchasedCount: purchasedBundles.length,
    completedCount: purchasedBundles.filter(p => p.calculatedProgress.progressPercentage === 100).length,
    totalSpent: purchasedBundles.reduce((sum, p) => sum + p.bundle.price, 0)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-96 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent mb-2">
            Quiz Bundles
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Discover curated quiz collections to boost your learning
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Available</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.availableCount}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Purchased</p>
                  <p className="text-2xl font-bold text-green-600">{stats.purchasedCount}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.completedCount}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Spent</p>
                  <p className="text-2xl font-bold text-orange-600">₹{stats.totalSpent}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-col md:flex-row gap-4 mb-6"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search bundles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="Mathematics">Mathematics</SelectItem>
              <SelectItem value="Science">Science</SelectItem>
              <SelectItem value="English">English</SelectItem>
              <SelectItem value="General Knowledge">General Knowledge</SelectItem>
            </SelectContent>
          </Select>

          <Select value={levelFilter} onValueChange={setLevelFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="Beginner">Beginner</SelectItem>
              <SelectItem value="Intermediate">Intermediate</SelectItem>
              <SelectItem value="Advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>

          {activeTab === 'available' && (
            <Select value={priceFilter} onValueChange={setPriceFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Price" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="free">Free</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="under-500">Under ₹500</SelectItem>
                <SelectItem value="500-1000">₹500 - ₹1000</SelectItem>
                <SelectItem value="over-1000">Over ₹1000</SelectItem>
              </SelectContent>
            </Select>
          )}
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="available">Available Bundles ({filteredAvailableBundles.length})</TabsTrigger>
            <TabsTrigger value="purchased">My Bundles ({filteredPurchasedBundles.length})</TabsTrigger>
          </TabsList>

          {/* Available Bundles */}
          <TabsContent value="available" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {filteredAvailableBundles.length === 0 ? (
                <div className="col-span-full">
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardContent className="p-12 text-center">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                        No bundles found
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {searchTerm || categoryFilter !== 'all' || levelFilter !== 'all' || priceFilter !== 'all'
                          ? "No bundles match your current filters."
                          : "No quiz bundles are available at the moment."
                        }
                      </p>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                filteredAvailableBundles.map((bundle, index) => (
                  <motion.div
                    key={bundle.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <BundleCard
                      bundle={bundle}
                      onPurchaseSuccess={handlePurchaseSuccess}
                    />
                  </motion.div>
                ))
              )}
            </motion.div>
          </TabsContent>

          {/* Purchased Bundles */}
          <TabsContent value="purchased" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {filteredPurchasedBundles.length === 0 ? (
                <div className="col-span-full">
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardContent className="p-12 text-center">
                      <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                        No purchased bundles
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {searchTerm || categoryFilter !== 'all' || levelFilter !== 'all'
                          ? "No purchased bundles match your current filters."
                          : "You haven't purchased any quiz bundles yet."
                        }
                      </p>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                filteredPurchasedBundles.map((purchase, index) => (
                  <motion.div
                    key={purchase.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <BundleCard
                      bundle={purchase.bundle}
                      isPurchased={true}
                      progress={purchase.calculatedProgress}
                      onPurchaseSuccess={handlePurchaseSuccess}
                    />
                  </motion.div>
                ))
              )}
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
